using Cherish.Core.Constants;
using Cherish.Core.Entities;
using Cherish.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Security.Claims;

namespace Cherish.Persistence;

public static class DatabaseInitializer
{
    public static async Task InitializeAsync(IServiceProvider serviceProvider, ILogger logger)
    {
        var context = serviceProvider.GetRequiredService<ApplicationDbContext>();
        var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = serviceProvider.GetRequiredService<RoleManager<ApplicationRole>>();

        await SeedDefaultPermissionsAsync(serviceProvider, logger);

        // Create default roles
        string[] roleNames = { "SuperAdmin", "Admin", "RewardAdmin", "User", "Employee" };
        foreach (var roleName in roleNames)
        {
            if (!await roleManager.RoleExistsAsync(roleName))
            {
                var role = new ApplicationRole
                {
                    Name = roleName,
                    Description = $"{roleName} role"
                };
                await roleManager.CreateAsync(role);
            }
        }

        // Create SuperAdmin user
        var superAdmin = new ApplicationUser
        {
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FirstName = "Super",
            LastName = "Admin",
            EmailConfirmed = true
        };

        if (await userManager.FindByEmailAsync(superAdmin.Email) == null)
        {
            var result = await userManager.CreateAsync(superAdmin, "SuperAdmin@123");
            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(superAdmin, "SuperAdmin");
            }
        }

        // Create default tenant
        var defaultTenant = new Tenant
        {
            Name = "Default Tenant",
            Domain = "default.cherish.com"
        };

        if (!context.Tenants.Any())
        {
            context.Tenants.Add(defaultTenant);
            await context.SaveChangesAsync();
        }

        // Create default admin for the tenant
        var admin = new ApplicationUser
        {
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FirstName = "Default",
            LastName = "Admin",
            EmailConfirmed = true,
            TenantId = defaultTenant.Id
        };

        if (await userManager.FindByEmailAsync(admin.Email) == null)
        {
            var result = await userManager.CreateAsync(admin, "Admin@123");
            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(admin, "Admin");
            }
        }

        // Create default reward admin for the tenant
        var rewardAdmin = new ApplicationUser
        {
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FirstName = "Reward",
            LastName = "Admin",
            EmailConfirmed = true,
            TenantId = defaultTenant.Id
        };

        if (await userManager.FindByEmailAsync(rewardAdmin.Email) == null)
        {
            var result = await userManager.CreateAsync(rewardAdmin, "RewardAdmin@123");
            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(rewardAdmin, "RewardAdmin");
            }
        }

        // Create default user for the tenant
        var user = new ApplicationUser
        {
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FirstName = "Default",
            LastName = "User",
            EmailConfirmed = true,
            TenantId = defaultTenant.Id
        };

        if (await userManager.FindByEmailAsync(user.Email) == null)
        {
            var result = await userManager.CreateAsync(user, "User@123");
            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(user, "User");
            }
        }

        // Create default employee for the tenant
        var employee = new ApplicationUser
        {
            UserName = "<EMAIL>",
            Email = "<EMAIL>",
            FirstName = "Default",
            LastName = "Employee",
            EmailConfirmed = true,
            TenantId = defaultTenant.Id
        };

        if (await userManager.FindByEmailAsync(employee.Email) == null)
        {
            var result = await userManager.CreateAsync(employee, "Employee@123");
            if (result.Succeeded)
            {
                await userManager.AddToRoleAsync(employee, "Employee");
            }
        }

        // Create UserPoints for each user
        if (!context.UserPoints.Any())
        {
            // Get all users
            var users = await userManager.Users.ToListAsync();
            foreach (var appUser in users)
            {
                if (appUser.TenantId.HasValue)
                {
                    var userPoints = new UserPoints
                    {
                        UserId = appUser.Id,
                        TenantId = appUser.TenantId.Value,
                        AvailablePoints = 0,
                        TotalEarnedPoints = 0,
                        TotalRedeemedPoints = 0
                    };

                    context.UserPoints.Add(userPoints);
                }
            }
            await context.SaveChangesAsync();
        }

        // Create sample rewards for the default tenant
        if (!context.Rewards.Any() && defaultTenant != null)
        {
            var rewardAdminId = (await userManager.FindByEmailAsync("<EMAIL>"))?.Id;

            if (!string.IsNullOrEmpty(rewardAdminId))
            {
                var sampleRewards = new List<Reward>
                {
                    new Reward
                    {
                        Title = "Employee of the Month",
                        Description = "Recognition for outstanding performance",
                        PointsValue = 100,
                        Type = RewardType.Recognition,
                        Status = RewardStatus.Active,
                        TenantId = defaultTenant.Id,
                        CreatedById = rewardAdminId
                    },
                    new Reward
                    {
                        Title = "Innovation Award",
                        Description = "Recognition for innovative ideas and solutions",
                        PointsValue = 75,
                        Type = RewardType.Badge,
                        Status = RewardStatus.Active,
                        TenantId = defaultTenant.Id,
                        CreatedById = rewardAdminId
                    },
                    new Reward
                    {
                        Title = "Team Player",
                        Description = "Recognition for exceptional collaboration",
                        PointsValue = 50,
                        Type = RewardType.Certificate,
                        Status = RewardStatus.Active,
                        TenantId = defaultTenant.Id,
                        CreatedById = rewardAdminId
                    },
                    new Reward
                    {
                        Title = "Coffee Gift Card",
                        Description = "Redeem points for a coffee gift card",
                        PointsValue = 25,
                        Type = RewardType.Points,
                        Status = RewardStatus.Active,
                        TenantId = defaultTenant.Id,
                        CreatedById = rewardAdminId
                    }
                };

                await context.Rewards.AddRangeAsync(sampleRewards);
                await context.SaveChangesAsync();
            }
        }

        // Assign permissions to roles
        var superAdminRole = await roleManager.FindByNameAsync("SuperAdmin");
        var adminRole = await roleManager.FindByNameAsync("Admin");
        var rewardAdminRole = await roleManager.FindByNameAsync("RewardAdmin");
        var userRole = await roleManager.FindByNameAsync("User");
        var employeeRole = await roleManager.FindByNameAsync("Employee");

        if (superAdminRole != null)
        {
            // SuperAdmin gets all permissions
            foreach (var permission in DefaultPermissions.GetAllPermissions())
            {
                await roleManager.AddClaimAsync(superAdminRole, new Claim("Permission", permission));
            }
        }

        if (adminRole != null)
        {
            // Admin gets complete access to all platform features
            var adminPermissions = DefaultPermissions.AdminPermissions;
            foreach (var permission in adminPermissions)
            {
                await roleManager.AddClaimAsync(adminRole, new Claim("Permission", permission));
            }
        }

        if (rewardAdminRole != null)
        {
            // RewardAdmin gets reward management permissions
            var rewardAdminPermissions = DefaultPermissions.RewardAdminPermissions;
            foreach (var permission in rewardAdminPermissions)
            {
                await roleManager.AddClaimAsync(rewardAdminRole, new Claim("Permission", permission));
            }
        }

        if (userRole != null)
        {
            // User gets basic reward permissions
            var userPermissions = DefaultPermissions.UserPermissions;
            foreach (var permission in userPermissions)
            {
                await roleManager.AddClaimAsync(userRole, new Claim("Permission", permission));
            }
        }

        if (employeeRole != null)
        {
            // Employee gets basic user permissions
            var employeePermissions = DefaultPermissions.EmployeePermissions;
            foreach (var permission in employeePermissions)
            {
                await roleManager.AddClaimAsync(employeeRole, new Claim("Permission", permission));
            }
        }
    }

    private static async Task SeedDefaultPermissionsAsync(IServiceProvider services, ILogger logger)
    {
        var dbContext = services.GetRequiredService<ApplicationDbContext>();

        // Check if permissions already exist
        if (await dbContext.Permissions.AnyAsync())
        {
            return;
        }

        logger.LogInformation("Seeding default permissions");

        var permissions = DefaultPermissions.GetAllPermissionObjects();
        await dbContext.Permissions.AddRangeAsync(permissions);
        await dbContext.SaveChangesAsync();

        logger.LogInformation("Default permissions seeded successfully");
    }
}

